from datetime import datetime

from fastapi.testclient import TestClient

from main import app
from src.tools.get_property_info import (
    Address,
    Doorway,
    GeoLocation,
    KeySellingPoints,
    Leasing,
    Location,
    PetPolicy,
    Pets,
    Property,
    PropertyData,
    PropertyResponse,
    Social,
)

client = TestClient(app)


def test_get_property_info_endpoint():
    """Test the GET /propertyinfo/{property_id} endpoint"""
    property_id = 123
    response = client.get(f"/propertyinfo/{property_id}")

    assert response.status_code == 200
    data = response.json()

    # Verify response structure
    assert "property" in data
    assert "status_code" in data
    assert data["status_code"] == "ok"

    # Verify property data
    property_data = data["property"]
    assert property_data["id"] == property_id
    assert "created_time" in property_data
    assert "data" in property_data

    # Verify nested property data
    nested_data = property_data["data"]
    assert nested_data["property_id"] == property_id
    assert "doorway" in nested_data
    assert "key_selling_points" in nested_data
    assert "location" in nested_data
    assert "pets" in nested_data


def test_property_response_model():
    """Test the PropertyResponse Pydantic model"""
    # Create a minimal valid property response
    property_response = PropertyResponse(
        property=Property(
            created_time=datetime.now(),
            data=PropertyData(
                doorway=Doorway(
                    agentGuidedTourDisclaimerText="",
                    applyNowIsActive=True,
                    availabilityIsActive=True,
                    bannerText="Test Banner",
                    customAppointmentMessage="",
                    dynamicNumberInsertionType="",
                    excludeAppointmentMessagePhone=False,
                    faqsIsActive=True,
                    formattedNumber="",
                    formattedNumberIsActive=False,
                    galleryIsActive=True,
                    hideKnockBranding=False,
                    hidePricing=False,
                    hideUnitPreferences=False,
                    hideUnitSelector=False,
                    includeInQuickLaunchMenuList=[],
                    leasingInfoIsActive=True,
                    leasingSpecialIsActive=False,
                    limitAvailableUnitsShown=False,
                    liveVideoTourDisclaimerText="",
                    neighborhoodIsActive=True,
                    petInfoIsActive=True,
                    renterPortalIsActive=True,
                    requestTextIsActive=True,
                    residentPortalURL="https://example.com",
                    scheduleATourContentBox="",
                    selfGuidedTourDisclaimerText="",
                    showNoteInput=False,
                    somethingElseIsActive=True,
                    useCustomWebsite=False,
                    useDynamicFloorplans=True,
                ),
                id="test_id",
                key_selling_points=KeySellingPoints(
                    community=["Test community point"],
                    location=["Test location point"],
                    units=["Test unit point"],
                ),
                leasing=Leasing(
                    application={
                        "fee": "$50",
                        "instructions": "Test",
                        "isDefaultApplicationUrlOverridden": False,
                        "link": "",
                    },
                    provider="Test Provider",
                    qualificationCriteria="Test criteria",
                    terms={
                        "breakPenalty": "Test",
                        "deposit": "$500",
                        "includeUpcharges": False,
                        "leaseLengths": [],
                        "leasingSpecial": "Test special",
                        "notes": "",
                    },
                ),
                location=Location(
                    address=Address(
                        city="Test City",
                        house="",
                        neighborhood="Test Area",
                        raw="123 Test St",
                        state="CA",
                        street="123 Test St",
                        zip="12345",
                    ),
                    geo=GeoLocation(coordinates=[-122.0, 37.0], type="point"),
                    name="Test Property",
                    needs_review=False,
                    numberOfUnits=100,
                    timezone="America/Los_Angeles",
                    yearBuilt=2020,
                ),
                logos=[],
                notes="",
                pets=Pets(
                    allowed=PetPolicy(
                        cats=True, large_dogs=False, none=False, small_dogs=True
                    ),
                    deposit="$300",
                    fee="$50",
                    notes="Test pet notes",
                    rent="$30",
                ),
                property_id=123,
                social=Social(
                    facebook="",
                    knock_email="<EMAIL>",
                    shortlink="https://example.com/short",
                    website="https://example.com",
                ),
            ),
            id=123,
            is_deleted=False,
            leasing_team_id=1000,
            modified_time=datetime.now(),
            owning_manager_id=2000,
            public_id="test_public_id",
            resource_id=3000,
            timezone="America/Los_Angeles",
            type="multi-family",
        ),
        status_code="ok",
    )

    assert property_response.property.id == 123
    assert property_response.status_code == "ok"
    assert property_response.property.data.pets.allowed.cats is True
    assert property_response.property.data.pets.allowed.large_dogs is False


def test_invalid_property_id():
    """Test the endpoint with an invalid property ID"""
    response = client.get("/propertyinfo/invalid_id")
    assert response.status_code == 422  # FastAPI validation error


def test_nonexistent_property_id():
    """Test the endpoint with a property ID that doesn't exist"""
    response = client.get("/propertyinfo/999999")
    assert (
        response.status_code == 200
    )  # Since we're using mock data, it should still return 200
    data = response.json()
    assert data["property"]["id"] == 999999


def test_multiple_property_ids():
    """Test that the endpoint rejects requests with multiple property IDs"""
    # Test with comma-separated IDs
    response = client.get("/propertyinfo/1,2,3")
    assert response.status_code == 422  # FastAPI validation error - invalid integer

    # Test with array-style IDs
    response = client.get("/propertyinfo/[1,2,3]")
    assert response.status_code == 422  # FastAPI validation error - invalid integer
