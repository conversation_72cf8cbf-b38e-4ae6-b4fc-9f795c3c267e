from __future__ import annotations

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field

__all__ = [
    "ScheduleTourRequest",
    "ScheduleTourResponse",
]


class ScheduleTourRequest(BaseModel):
    """Payload required to schedule a property tour."""

    property_id: int = Field(..., description="Knock property ID")
    renter_id: int = Field(..., description="Prospect renter ID (UUID / string)")
    tour_date: datetime = Field(
        ..., description="Requested tour datetime in *property* timezone"
    )
    first_name: str = Field(..., description="Prospect first name")
    last_name: str = Field(..., description="Proscpect last name")
    preference_bedrooms: Optional[int] = Field(
        None, ge=0, le=4, description="Desired bedroom count (0-4)"
    )
    preference_move_date: Optional[str] = Field(
        None, description="Preferred move-in date (YYYY-MM-DD)"
    )


class ScheduleTourResponse(BaseModel):
    """Response returned after scheduling a tour"""

    status: str = Field(..., description="Status string, e.g. 'success', 'error'")
    appointment_id: str = Field(..., description="Appointment identifier")
    message: str = Field(..., description="Human-readable message")
