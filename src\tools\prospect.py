"""
Guest card update tools for the Knock MCP Server.

This module contains FastAPI endpoints for guest card operations that will be
automatically converted into MCP tools by FastApiMCP.
"""

from fastapi import APIRouter
from fastmcp import FastMCP
from pydantic import BaseModel

from src.utils.log_config import get_logger

router = APIRouter()
logger = get_logger(__name__)


class GuestCardPhone(BaseModel):
    phone_number: str | None = None


class GuestCardProfile(BaseModel):
    first_name: str
    last_name: str
    target_move_date: str | None = None  # YYYY-MM-DD
    email: str | None = None
    phone: GuestCardPhone | None = None


class GuestCardPreferences(BaseModel):
    bedrooms: list[str] | None = None


class GuestCardRequestType(BaseModel):
    """
    Request type for updating a guest card.
    """

    profile: GuestCardProfile
    preferences: GuestCardPreferences | None = None


class ProspectStatusResponse(BaseModel):
    """
    Response type for updating prospect status.
    """

    prospect_id: int
    status: str


class ProspectActivityResponse(BaseModel):
    """
    Response type for adding activity to a prospect.
    """

    prospect_id: int
    activity: str


def update_prospect_guestcard(
    prospect_id: int, profile: GuestCardProfile, preferences: GuestCardPreferences | None = None
) -> GuestCardRequestType:
    """
    Update an existing prospect guestcard.

    This endpoint updates the specified fields of a guestcard.
    Only the provided fields will be updated.

    Only the provided fields will be updated.
    """
    logger.info(f"Starting guestcard update for prospect_id: {prospect_id}")
    logger.debug(f"Request data profile: {profile}")
    logger.debug(f"Request preferences: {preferences}")

    # Simulate fetching existing card and applying updates
    return GuestCardRequestType(profile=profile, preferences=preferences)


def register_prospect_tools(mcp: FastMCP) -> None:
    """
    Register the prospect tools with the MCP server.
    """

    @mcp.tool(
        name="update_prospect_guestcard",
        description="Update an existing prospect guestcard.",
    )
    def update_guestcard(prospect_id: int, request: GuestCardRequestType) -> GuestCardRequestType:
        """
        Update an existing prospect guestcard.

        This endpoint updates the specified fields of a guestcard.
        Only the provided fields will be updated.
        """
        logger.info(f"Starting guestcard update for prospect_id: {prospect_id}")
        logger.debug(f"Request data: {request}")

        # Simulate fetching existing card and applying updates
        return update_prospect_guestcard(prospect_id, request.profile, request.preferences)


@router.put("/{prospect_id}/status", operation_id="update_prospect_status", response_model=ProspectStatusResponse)
async def update_prospect_status(prospect_id: int, status: str) -> ProspectStatusResponse:
    """
    Update the status of an existing prospect.

    This endpoint updates the status of the specified prospect.
    """
    logger.info(f"Updating prospect {prospect_id} status to {status}")
    return ProspectStatusResponse(prospect_id=prospect_id, status=status)


@router.post("/{prospect_id}/activity", operation_id="add_prospect_activity", response_model=ProspectActivityResponse)
async def add_prospect_activity(prospect_id: int, activity: str) -> ProspectActivityResponse:
    """
    Add an activity to the prospect's activity log.

    This endpoint adds a new activity to the specified prospect's activity log.
    """
    logger.info(f"Adding activity '{activity}' to prospect {prospect_id}")
    # Mock implementation - in real scenario, this would update database
    return ProspectActivityResponse(prospect_id=prospect_id, activity=activity)
