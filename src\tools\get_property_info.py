"""
get_knock_property_info tools for the Knock MCP Server.

This module contains FastAPI endpoints for guest card operations that will be
automatically converted into MCP tools by FastApiMCP.
"""

from datetime import datetime
from typing import Optional

from fastapi import APIRouter
from pydantic import BaseModel, Field

router = APIRouter()


class QuickLaunchMenuItem(BaseModel):
    enabled: bool
    name: str


class Doorway(BaseModel):
    agentGuidedTourDisclaimerText: str
    applyNowIsActive: bool
    availabilityIsActive: bool
    bannerText: str
    customAppointmentMessage: str
    dynamicNumberInsertionType: str
    excludeAppointmentMessagePhone: bool
    faqsIsActive: bool
    formattedNumber: str
    formattedNumberIsActive: bool
    galleryIsActive: bool
    hideKnockBranding: bool
    hidePricing: bool
    hideUnitPreferences: bool
    hideUnitSelector: bool
    includeInQuickLaunchMenuList: list[QuickLaunchMenuItem]
    leasingInfoIsActive: bool
    leasingSpecialIsActive: bool
    limitAvailableUnitsShown: bool
    liveVideoTourDisclaimerText: str
    neighborhoodIsActive: bool
    petInfoIsActive: bool
    renterPortalIsActive: bool
    requestTextIsActive: bool
    residentPortalURL: str
    scheduleATourContentBox: str
    selfGuidedTourDisclaimerText: str
    showNoteInput: bool
    somethingElseIsActive: bool
    useCustomWebsite: bool
    useDynamicFloorplans: bool


class KeySellingPoints(BaseModel):
    community: list[str]
    location: list[str]
    units: list[str]


class Application(BaseModel):
    fee: str
    instructions: str
    isDefaultApplicationUrlOverridden: bool
    link: str


class LeaseLength(BaseModel):
    isAvailable: bool
    leaseLength: str
    lengthUnit: Optional[str]


class LeasingTerms(BaseModel):
    breakPenalty: str
    deposit: str
    includeUpcharges: bool
    leaseLengths: list[LeaseLength]
    leasingSpecial: str
    notes: str


class Leasing(BaseModel):
    application: Application
    provider: str
    qualificationCriteria: str
    terms: LeasingTerms


class Address(BaseModel):
    city: str
    house: str
    neighborhood: str
    raw: str
    state: str
    street: str
    zip: str


class GeoLocation(BaseModel):
    coordinates: list[float]
    type: str


class Location(BaseModel):
    address: Address
    geo: GeoLocation
    name: str
    needs_review: bool
    numberOfUnits: int
    timezone: str
    yearBuilt: int


class Logo(BaseModel):
    isError: bool
    isLocal: bool
    progress: int
    url: str


class PetPolicy(BaseModel):
    cats: bool
    large_dogs: bool
    none: bool
    small_dogs: bool


class Pets(BaseModel):
    allowed: PetPolicy
    deposit: str
    fee: str
    notes: str
    rent: str


class Social(BaseModel):
    facebook: str
    knock_email: str
    shortlink: str
    website: str


class PropertyData(BaseModel):
    coverPhoto: dict = Field(default_factory=dict)
    customDetails: list = Field(default_factory=list)
    doorway: Doorway
    floorplan: dict = Field(default_factory=dict)
    id: str
    key_selling_points: KeySellingPoints
    leasing: Leasing
    location: Location
    logos: list[Logo]
    notes: str
    pets: Pets
    photos: list = Field(default_factory=list)
    property_id: int
    social: Social


class Property(BaseModel):
    created_time: datetime
    data: PropertyData
    id: int
    is_deleted: bool
    leasing_team_id: int
    modified_time: datetime
    owning_manager_id: int
    public_id: str
    resource_id: int
    timezone: str
    type: str


class PropertyResponse(BaseModel):
    property: Property
    status_code: str


@router.get(
    "/{property_id}",
    operation_id="get_prospect_property_info",
    response_model=PropertyResponse,
)
async def get_knock_property_info(property_id: int) -> PropertyResponse:
    """
    This endpoint fetches the details of a knock property.
    """
    # Mock implementation
    mock_property = Property(
        created_time=datetime.now(),
        data=PropertyData(
            doorway=Doorway(
                agentGuidedTourDisclaimerText="",
                applyNowIsActive=True,
                availabilityIsActive=True,
                bannerText="Welcome to our community!",
                customAppointmentMessage="",
                dynamicNumberInsertionType="",
                excludeAppointmentMessagePhone=False,
                faqsIsActive=True,
                formattedNumber="",
                formattedNumberIsActive=False,
                galleryIsActive=True,
                hideKnockBranding=False,
                hidePricing=False,
                hideUnitPreferences=False,
                hideUnitSelector=False,
                includeInQuickLaunchMenuList=[
                    QuickLaunchMenuItem(enabled=True, name="schedule"),
                    QuickLaunchMenuItem(enabled=True, name="gallery"),
                ],
                leasingInfoIsActive=True,
                leasingSpecialIsActive=True,
                limitAvailableUnitsShown=False,
                liveVideoTourDisclaimerText="",
                neighborhoodIsActive=True,
                petInfoIsActive=True,
                renterPortalIsActive=True,
                requestTextIsActive=True,
                residentPortalURL="https://example.com/portal",
                scheduleATourContentBox="",
                selfGuidedTourDisclaimerText="",
                showNoteInput=False,
                somethingElseIsActive=True,
                useCustomWebsite=False,
                useDynamicFloorplans=True,
            ),
            id=str(property_id),
            key_selling_points=KeySellingPoints(
                community=["Great amenities", "24/7 maintenance"],
                location=["Close to shopping", "Easy highway access"],
                units=["Modern appliances", "Spacious floor plans"],
            ),
            leasing=Leasing(
                application=Application(
                    fee="$50",
                    instructions="Apply online",
                    isDefaultApplicationUrlOverridden=False,
                    link="",
                ),
                provider="Mock Provider",
                qualificationCriteria="Standard qualification criteria applies",
                terms=LeasingTerms(
                    breakPenalty="Two months rent",
                    deposit="$500",
                    includeUpcharges=False,
                    leaseLengths=[LeaseLength(isAvailable=True, leaseLength="12", lengthUnit="months")],
                    leasingSpecial="First month free",
                    notes="",
                ),
            ),
            location=Location(
                address=Address(
                    city="Sample City",
                    house="",
                    neighborhood="Downtown",
                    raw="123 Main St",
                    state="CA",
                    street="123 Main St",
                    zip="12345",
                ),
                geo=GeoLocation(coordinates=[-122.419416, 37.774929], type="point"),
                name=f"Property {property_id}",
                needs_review=False,
                numberOfUnits=100,
                timezone="America/Los_Angeles",
                yearBuilt=2020,
            ),
            logos=[],
            notes="",
            pets=Pets(
                allowed=PetPolicy(cats=True, large_dogs=False, none=False, small_dogs=True),
                deposit="$300",
                fee="$50",
                notes="Weight limit 25 lbs",
                rent="$30",
            ),
            property_id=property_id,
            social=Social(
                facebook="",
                knock_email="<EMAIL>",
                shortlink="https://example.com/p/123",
                website="https://example.com",
            ),
        ),
        id=property_id,
        is_deleted=False,
        leasing_team_id=1000,
        modified_time=datetime.now(),
        owning_manager_id=2000,
        public_id=f"prop_{property_id}",
        resource_id=3000,
        timezone="America/Los_Angeles",
        type="multi-family",
    )

    return PropertyResponse(property=mock_property, status_code="ok")
