# Knock MCP Server Makefile

.PHONY: help install install-dev run run-dev test lint format type-check clean docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install production dependencies"
	@echo "  install-dev  - Install all dependencies including dev tools"
	@echo "  run          - Run the application in production mode"
	@echo "  run-dev      - Run the application in development mode with auto-reload"
	@echo "  test         - Run tests with coverage"
	@echo "  lint         - Run linting checks"
	@echo "  format       - Format code"
	@echo "  type-check   - Run type checking"
	@echo "  clean        - Clean up cache and temporary files"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"

# Install production dependencies
install:
	uv sync

# Install all dependencies including development tools
install-dev:
	uv sync --all-extras --dev
	uv run pre-commit install

# Run the application in production mode
run:
	uv run uvicorn main:app --host 0.0.0.0 --port 8000

# Run the application in development mode with auto-reload
run-dev:
	uv run python main.py

# Run tests with coverage
test:
	uv run pytest

# Run linting checks
lint:
	uv run ruff check --fix src/

# Format code
format:
	uv run ruff format src/

# Run type checking
type-check:
	uv run mypy src/

# Run pre-commit hooks on all files
pre-commit:
	uv run pre-commit run --all-files

# Clean up cache and temporary files
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	find . -type d -name ".ruff_cache" -exec rm -rf {} +
	rm -rf .coverage
	rm -rf htmlcov/

# Build Docker image
docker-build:
	docker build -t knock-mcp-server .

# Run Docker container
docker-run:
	docker run -p 8000:8000 knock-mcp-server

# Setup development environment (install dev dependencies and pre-commit hooks)
setup-dev: install-dev

# Quick development workflow (format, lint, type-check, test)
dev-workflow: format lint type-check test

# Start development server with all checks
dev: dev-workflow run-dev
