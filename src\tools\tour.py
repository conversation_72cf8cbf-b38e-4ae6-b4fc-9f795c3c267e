from __future__ import annotations

from datetime import datetime
from fastapi import APIRouter, Query

from src.models.tour import ScheduleTourRequest, ScheduleTourResponse

router = APIRouter(prefix="/tour", tags=["Tour Scheduling"])

@router.post("/schedule", response_model=ScheduleTourResponse)
async def schedule_tour(request: ScheduleTourRequest) -> ScheduleTourResponse:
    if request.property_id is None:
        return {
            "status": "error",
            "message": "Property ID is required"
        }

    composite_key = f"{request.property_id}:{request.tour_date.isoformat()}"
    appointment_id = abs(hash(composite_key)) % 10000000

    return ScheduleTourResponse(
        status="success",
        appointment_id=str(appointment_id),  # Convert integer to string to match the expected type
        message="This is a mock response.",
    )
